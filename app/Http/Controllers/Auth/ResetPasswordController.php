<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Auth\Authenticate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ResetPasswordController extends Authenticate
{
    // Password Reset
    public function resetPassword()
    {
        return auth()->check()
                ? redirect($this->homePage())
                : Inertia::render('auth/ForgotPassword', [
                    'status' => session('status'),
                ]);
    }

    // Send Reset Email
    public function sendEmail(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
                    ? redirect()->back()->with(['message' => 'Password reset link sent! Please check your email to continue with password recovery'])
                    : back()->withError(['email' => __($status)]);
    }

    // Reset Password Form
    public function resetForm(Request $request)
    {
        return Inertia::render('auth/ResetPassword', [
            'email' => $request->email,
            'token' => $request->route('token'),
        ]);
    }

    // Reset Password Handler
    public function resetHandler(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => [
                'nullable',
                'min:8',
                'max:20',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/',
                'confirmed',
            ],
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                ])->setRememberToken(Str::random(60));

                $user->save();
            }
        );

        return $status === Password::PASSWORD_RESET
            ? redirect()->route('login')->with(['message' => 'Password updated successfully'])
            : back()->withErrors(['email' => [__($status)]]);
    }
}

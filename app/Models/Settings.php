<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Settings extends Model
{
    use HasFactory;

    protected $table = 'settings';

    protected $primaryKey = 'shopid';

    protected $fillable = [
        'motor',
        'failedpayment',
        'pph',
        'oss',
    ];

    const CREATED_AT = null;

    const UPDATED_AT = null;
}
